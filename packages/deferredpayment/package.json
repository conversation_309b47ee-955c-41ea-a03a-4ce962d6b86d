{"name": "@skywind-group/sw-management-deferredpayment", "version": "2.138.0-develop", "description": "Contains deferred payment inegration functionality", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "dependencies": {"@skywind-group/sw-deferred-payment": "^2.0.0", "@skywind-group/sw-deferred-payment-cache": "^2.0.0", "@skywind-group/sw-deferred-payment-client": "^2.0.0", "@skywind-group/sw-management-playersession": "~2.138.0-develop", "@skywind-group/sw-messaging": "0.2.4"}, "devDependencies": {"@skywind-group/sw-wallet-adapter-core": "2.1.5", "generic-pool": "3.9.0", "ioredis": "5.5.0"}}