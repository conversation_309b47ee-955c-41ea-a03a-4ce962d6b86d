import { expect } from "chai";
import * as sinon from "sinon";
import { BaseGameUrlStrategy } from "../../../skywind/services/gameUrl/baseGameUrlStrategy";
import { EntityGame } from "../../../skywind/entities/game";
import { BrandEntity } from "../../../skywind/entities/brand";
import { EntitySettings } from "../../../skywind/entities/settings";
import { PlayMode, StartGameTokenData, MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { ClientPayload } from "../../../skywind/services/gameUrl/getGameURLInfo";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import * as entityDomainService from "../../../skywind/services/entityDomainService";
import * as EntityJurisdictionCache from "../../../skywind/cache/entityJurisdiction";
import { UrlPlaceholders } from "../../../skywind/services/gameUrl/urlPlaceholders";
import { CountrySource } from "../../../skywind/utils/countrySource";

interface InnerStartGameURLInfo {
    urlParams: any;
    tokenData: StartGameTokenData | undefined;
}

class TestGameUrlStrategy extends BaseGameUrlStrategy {
    private testCurrency: string = "USD";

    public setTestCurrency(currency: string) {
        this.testCurrency = currency;
    }

    protected async validateBonusCoinsAvailable(tokenData: StartGameTokenData | undefined, playMode: PlayMode): Promise<void> {
        // Mock implementation for testing
    }

    protected async getTokenData(payload: ClientPayload): Promise<InnerStartGameURLInfo> {
        return {
            urlParams: {},
            tokenData: {
                playerCode: "TEST_PLAYER",
                gameCode: "TEST_GAME",
                currency: this.testCurrency,
                playmode: payload.playmode || PlayMode.REAL,
                brandId: 1
            } as StartGameTokenData
        };
    }

    protected getCurrency(tokenData: StartGameTokenData | undefined): string {
        return tokenData?.currency || this.testCurrency;
    }

    protected getLanguage(tokenData: StartGameTokenData | undefined, payload: ClientPayload): string {
        return "en";
    }

    protected async getCountrySource(tokenData: StartGameTokenData | undefined, ip?: string, isLiveGame?: boolean): Promise<CountrySource> {
        return {
            whitelisted: false,
            restricted: false,
            source: "test",
            code: "US"
        };
    }
}

describe("BaseGameUrlStrategy", () => {
    let strategy: TestGameUrlStrategy;
    let entityGame: EntityGame;
    let brand: BrandEntity;
    let entitySettings: EntitySettings;
    let currenciesValueStub: sinon.SinonStub;
    let getEntityDomainServiceStub: sinon.SinonStub;
    let entityJurisdictionCacheStub: sinon.SinonStub;
    let urlPlaceholdersReplaceStub: sinon.SinonStub;

    beforeEach(() => {
        entityGame = {
            id: 1,
            game: {
                id: 1,
                code: "TEST_GAME",
                title: "Test Game",
                url: "http://test.game.com/{playmode}",
                gameProvider: { code: "TestProvider" }
            },
            isLiveGame: () => false
        } as EntityGame;

        brand = {
            id: 1,
            underMaintenance: () => false,
            isMerchant: false
        } as BrandEntity;

        entitySettings = {
            urlParams: {},
            launchGameInsideLobby: false
        } as EntitySettings;

        // Create strategy instance using reflection to bypass protected constructor
        strategy = Object.create(TestGameUrlStrategy.prototype);
        TestGameUrlStrategy.call(strategy, entityGame, brand, entitySettings, false);

        // Stub external dependencies
        currenciesValueStub = sinon.stub(Currencies, "value");
        getEntityDomainServiceStub = sinon.stub(entityDomainService, "getEntityDomainService");
        entityJurisdictionCacheStub = sinon.stub(EntityJurisdictionCache, "findOne");
        urlPlaceholdersReplaceStub = sinon.stub(UrlPlaceholders, "replace");

        // Setup default stubs
        getEntityDomainServiceStub.returns({
            get: sinon.stub().resolves({ domain: "dynamic.test.com" })
        });
        entityJurisdictionCacheStub.resolves(null);
        urlPlaceholdersReplaceStub.returns("http://test.game.com/test");
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("getUrl", () => {
        it("should set playMode to FUN_BONUS when currency has funBonus property", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.REAL
            } as MerchantGameInitRequest;

            // Set test currency to FUNUSD
            strategy.setTestCurrency("FUNUSD");

            // Mock currency with funBonus property
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.FUN_BONUS);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("fun_bonus");
        });

        it("should not change playMode when currency does not have funBonus property", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.REAL
            } as MerchantGameInitRequest;

            // Set test currency to USD
            strategy.setTestCurrency("USD");

            // Mock currency without funBonus property
            currenciesValueStub.withArgs("USD").returns({
                name: "US Dollar",
                iso: "USD"
                // No funBonus property
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.REAL);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });

        it("should handle FUN_BONUS playMode when explicitly set", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.FUN_BONUS
            } as MerchantGameInitRequest;

            // Set test currency to FUNUSD
            strategy.setTestCurrency("FUNUSD");

            // Mock currency with funBonus property
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.FUN_BONUS);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("fun_bonus");
        });

        it("should convert REAL playMode to FUN_BONUS when currency has funBonus=true", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.REAL
            } as MerchantGameInitRequest;

            // Set test currency to FUNUSD
            strategy.setTestCurrency("FUNUSD");

            // Mock currency with funBonus property
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.FUN_BONUS);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("fun_bonus");
        });

        it("should convert FUN playMode to FUN_BONUS when currency has funBonus=true", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.FUN
            } as MerchantGameInitRequest;

            // Set test currency to FUNUSD
            strategy.setTestCurrency("FUNUSD");

            // Mock currency with funBonus property
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.FUN_BONUS);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("fun_bonus");
        });

        it("should not convert to FUN_BONUS when currency has funBonus=false", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.REAL
            } as MerchantGameInitRequest;

            // Set test currency to USD
            strategy.setTestCurrency("USD");

            // Mock currency with funBonus=false
            currenciesValueStub.withArgs("USD").returns({
                name: "US Dollar",
                iso: "USD",
                funBonus: false
            });

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.REAL);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });

        it("should handle currency that returns null from Currencies.value", async () => {
            // Arrange
            const payload: ClientPayload = {
                playmode: PlayMode.REAL
            } as MerchantGameInitRequest;

            // Set test currency to UNKNOWN
            strategy.setTestCurrency("UNKNOWN");

            // Mock currency that returns null
            currenciesValueStub.withArgs("UNKNOWN").returns(null);

            // Setup URL replacement to capture the playMode parameter
            urlPlaceholdersReplaceStub.callsFake((url, params) => {
                expect(params.playMode).to.equal(PlayMode.REAL);
                return `http://test.game.com/${params.playMode}`;
            });

            // Stub other required methods
            sinon.stub(strategy as any, "getDefaultLobbyId").resolves(null);
            sinon.stub(strategy as any, "getStaticDomainHost").resolves("static.test.com");
            sinon.stub(strategy as any, "getHistoryUrlInfo").returns({});
            sinon.stub(strategy as any, "replaceClientVersionWithDeploymentGroup").callsFake((url) => Promise.resolve(url));
            sinon.stub(strategy as any, "disableCashierAndLobby").returns(undefined);
            sinon.stub(strategy as any, "addCustomGameSplash").returns(undefined);
            sinon.stub(strategy as any, "disableBalancePing").returns(undefined);

            // Act
            const result = await strategy.getUrl(payload);

            // Assert
            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });
    });
});
