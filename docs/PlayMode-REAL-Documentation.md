# PlayMode.REAL Documentation

## Overview

`PlayMode.REAL` is the default and primary play mode in the sw-management-api system that enables games to operate with real money currencies. This mode represents standard gambling operations where players use actual money to place bets and receive real money winnings.

### Purpose
- Enable real money gambling operations
- Support standard casino and gaming functionality
- Provide full feature access including jackpots and transfers
- Maintain regulatory compliance for real money gaming
- Support all payment and wallet operations

## PlayMode Enum Structure

The PlayMode enum in `@skywind-group/sw-wallet-adapter-core` defines these values:

```typescript
export declare enum PlayMode {
    REAL = "real",           // Real money gameplay (default)
    FUN = "fun",            // Fun/demo mode
    BNS = "bns",            // Bonus coins gameplay
    PLAY_MONEY = "play_money" // Virtual/social currency gameplay
}
```

## Configuration

### Default Behavior
- `PlayMode.REAL` is the default mode when no play mode is specified
- No special merchant configuration required (unlike PLAY_MONEY mode)
- All standard gaming features are available

### Validation
```typescript
// validatePlaymode.ts - REAL mode always passes validation
export function validatePlaymode(brand: BrandEntity, playmode: PlayMode = PlayMode.REAL, merchant?: Merchant): boolean {
    if (playmode !== PlayMode.PLAY_MONEY) {
        return true;  // REAL mode validation always succeeds
    }
    // ... PLAY_MONEY specific validation
}
```

## Business Logic Features

### 1. Jackpot Support
Real mode has full jackpot support:

```typescript
// In jackpot.ts
public static supportJP(gameMode: GameMode) {
    return gameMode === "real" || !gameMode;  // REAL mode supports jackpots
}
```

**Key Points:**
- Full jackpot functionality available
- Progressive jackpot contributions
- Jackpot win processing
- Multi-level jackpot support
- Game-owned and external jackpots
- Instant jackpot mechanics

### 2. RTP (Return to Player) Configuration
Real mode supports full RTP configuration and deduction:

```typescript
// In gameRTPHistory.ts
public playModeSupported(playmode: PlayMode, entitySettings: EntitySettings) {
    return playmode === PlayMode.REAL || (playmode === PlayMode.FUN && entitySettings.rtpDeductionFunModeEnabled);
}
```

**RTP Features:**
- Dynamic RTP configuration support
- RTP deduction capabilities for operator margins
- Theoretical RTP calculations
- Feature-specific RTP settings
- Base RTP and RTP range support

### 3. Currency Exchange
Standard currency exchange logic applies:

```typescript
// In PlayMode.exchange()
// For REAL mode, falls through to standard exchange logic
return getCurrencyExchange()
    .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
```

**Features:**
- Full currency conversion support
- Real-time exchange rates
- All supported currencies available
- No restrictions on currency types

### 4. Slot Game Mechanics
Real mode supports complete slot functionality:

**Slot Configuration:**
```typescript
interface SlotGameLimits extends StakedLimits {
    maxTotalStake: number;     // Maximum total bet amount
    winMax: number;            // Maximum win amount
    defaultTotalStake?: number; // Default total stake
    coins?: number[];          // Available coin values
    defaultCoin?: number;      // Default coin value
    alignCurrencies?: boolean; // Currency alignment support
}
```

**Slot Features:**
- Full reel mechanics with configurable rows and symbols
- Wild symbols with substitution logic and multipliers
- Scatter symbols triggering bonus features
- Free spins with enhanced mechanics
- Payline calculations and win evaluations
- Bonus rounds and feature games
- Symbol multipliers and special effects

### 5. Transfer Operations
Complete transfer functionality:

```typescript
// Transfer enabled based on merchant and game configuration
settings.transferEnabled = isTransferEnabled(entityGame, merchant, playmode);
```

**Capabilities:**
- Transfer in/out operations
- External wallet integration
- Internal wallet management
- Multi-wallet support

### 6. Game Limits and Stakes
Real mode supports comprehensive betting limits:

**Stake Configuration:**
```typescript
interface StakedLimits extends Limits {
    stakeAll: number[];    // Available stake amounts
    stakeDef: number;      // Default stake
    stakeMax: number;      // Maximum stake
    stakeMin: number;      // Minimum stake
}
```

**Limit Features:**
- Dynamic stake calculations based on currency multipliers
- Configurable coin values and bet levels
- Maximum total stake enforcement
- Win limit controls
- Currency-specific limit adjustments
- Auto-play settings and spin options

## Integration Points

### 1. Play Service Selection
Real mode uses the standard play service:

```typescript
// In defaultPlayServiceFactory.ts
// REAL mode uses base MerchantPlayService or MerchantTransferablePlayService
// No special wrapper needed (unlike PLAY_MONEY or BNS modes)
```

### 2. Wallet Operations
Full wallet functionality:
- Real money balance management
- Transaction processing
- Payment operations
- Rollback and recovery support

### 3. Game URL Generation
Standard URL generation without restrictions:
- All game features available
- No special URL parameters needed
- Full lobby integration
- Complete cashier functionality

## Data Flow

### 1. Game Initialization
1. Default to REAL mode if not specified
2. Standard validation (no special requirements)
3. Full feature set available
4. Complete game context creation

### 2. Payment Processing
1. Real money transactions
2. Full payment validation
3. Complete audit trails
4. Regulatory compliance logging

### 3. Session Management
1. Standard session handling
2. Full recovery capabilities
3. Complete state management
4. All game features accessible

## Comparison with Other Modes

### REAL vs PLAY_MONEY
| Feature | REAL | PLAY_MONEY |
|---------|------|------------|
| Currency Type | Real money | Virtual/Social |
| Jackpots | ✅ Supported | ❌ Not supported |
| Transfers | ✅ Full support | ⚠️ Limited |
| Exchange | ✅ All currencies | ⚠️ Social only |
| Validation | ✅ Always valid | ⚠️ Requires config |
| RTP Deduction | ✅ Supported | ❌ Not supported |
| Slot Features | ✅ Complete | ✅ Complete |

### REAL vs BNS
| Feature | REAL | BNS |
|---------|------|-----|
| Currency | Player currency | BNS coins |
| Jackpots | ✅ Supported | ❌ Not supported |
| Exchange | ✅ Standard rates | ⚠️ Promo rates |
| Context | ✅ Persistent | ⚠️ Promo-specific |
| RTP Deduction | ✅ Supported | ❌ Not supported |
| Free Bets | ✅ Supported | ❌ Not supported |

### REAL vs FUN
| Feature | REAL | FUN |
|---------|------|-----|
| Money | ✅ Real money | ❌ Demo only |
| Jackpots | ✅ Supported | ❌ Not supported |
| Persistence | ✅ Full | ⚠️ Limited |
| Features | ✅ Complete | ⚠️ Demo subset |
| RTP Deduction | ✅ Supported | ⚠️ Optional |
| Slot Mechanics | ✅ Full | ✅ Full |

## Implementation Examples

### 1. Game Token for Real Mode
```typescript
const gameTokenData: GameTokenData = {
    playerCode: "PLAYER123",
    gameCode: "SLOT001", 
    brandId: 1,
    currency: "USD",
    playmode: "real",  // or PlayMode.REAL
    test: false
};
```

### 2. Payment Operation
```typescript
const paymentRequest: PaymentRequest = {
    transactionId: "TXN_123",
    bet: 100,  // $1.00 in cents
    win: 250,  // $2.50 in cents
    roundId: "ROUND_456"
};

// Processed through standard MerchantPlayService
const balance = await playService.commitGamePayment(gameTokenData, paymentRequest);
```

### 3. Slot Game Configuration
```typescript
// Example slot game configuration for real mode
const slotConfig = {
    scenes: [
        new SlotScene({
            name: 'main',
            symbols: {
                Wild: {id: 12, wild: true},
                Scatter: {id: 13, payId: 6, scatter: true, multiplier: [0, 0, 0, 0, 0]}
            },
            reels: {
                rows: [4, 4, 4, 4, 4],
                strips: [/* reel strips */]
            },
            rules: [
                new WildRule({
                    ids: [12],
                    symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
                    multiplier: [2, 2, 2, 2, 2]
                }),
                new ScatterRule({
                    ids: [13],
                    scene: 'freeSpins',
                    rewards: [
                        {freeSpins: 0, multiplier: 0},
                        {freeSpins: 12, multiplier: 3}
                    ],
                    betMultiplier: [0, 2, 4, 30, 400]
                })
            ]
        })
    ],
    paytable: [
        [0, 0, 6, 20, 200],
        [0, 0, 8, 24, 250],
        // ... more paytable entries
    ],
    lines: [
        [1, 1, 1, 1, 1],
        [0, 0, 0, 0, 0],
        // ... more paylines
    ]
};
```

### 4. Jackpot Integration
```typescript
// Jackpot initialization for real mode
const [jpnContext, gameJackpotInfo] = await initJackpot(gameData);
// Returns valid jackpot context and info for REAL mode
```

### 5. RTP Configuration Example
```typescript
// RTP settings for real mode
const rtpSettings = {
    rtp: 96.5,              // Base RTP percentage
    rtpDeduction: 2.0,      // Operator margin deduction
    featuresRTP: {
        freeSpins: { RTP: 15.2, rtpReducer: true },
        bonus: { RTP: 8.7, rtpReducer: false }
    }
};
```

## Error Handling

### Common Scenarios
1. **Insufficient Balance**: Standard wallet error handling
2. **Currency Issues**: Exchange rate validation
3. **Transaction Failures**: Complete rollback support
4. **Regulatory Blocks**: Compliance error handling

### Error Types
- `InsufficientEntityBalanceError`
- `BadTransactionId`
- `CurrencyExchangeError`
- `RegulatoryComplianceError`

## Security and Compliance

### 1. Real Money Protection
- Complete transaction audit trails
- Regulatory compliance logging
- Anti-fraud monitoring
- Balance verification

### 2. Financial Controls
- Transaction limits enforcement
- Currency validation
- Exchange rate verification
- Payment method validation

### 3. Regulatory Compliance
- Jurisdiction-specific rules
- Age verification requirements
- Responsible gaming features
- Transaction reporting

## Monitoring and Analytics

### 1. Financial Metrics
- Real money volume tracking
- Win/loss ratios
- Currency exchange impacts
- Payment method performance

### 2. Player Behavior
- Betting patterns
- Session duration
- Game preferences
- Conversion metrics

### 3. System Performance
- Transaction processing times
- Payment success rates
- System availability
- Error rates

## Best Practices

1. **Always validate currency** before processing real money transactions
2. **Implement proper error handling** for financial operations
3. **Maintain audit trails** for all real money activities
4. **Follow regulatory requirements** for the jurisdiction
5. **Monitor for fraud patterns** in real money gameplay
6. **Ensure proper balance verification** before and after transactions
7. **Implement proper rollback mechanisms** for failed transactions

## Slot Engine Specific Features

### 1. Game Mechanics
Real mode supports all slot engine features:

**Symbol Processing:**
- Wild symbol substitution with multipliers
- Scatter symbol detection and bonus triggering
- Symbol combination evaluation
- Payline win calculations

**Bonus Features:**
- Free spins with enhanced mechanics
- Bonus rounds and mini-games
- Progressive features and cascading wins
- Multi-stage game support

**RTP Management:**
- Dynamic RTP configuration
- Feature-specific RTP calculations
- Operator margin deductions
- Theoretical vs actual RTP tracking

### 2. Game State Management
```typescript
// Game context for real mode includes full state
const gameContext = {
    currentScene: "main",
    nextScene: "freeSpins",
    roundEnded: false,
    specialState: {
        freeSpinsRemaining: 10,
        multiplier: 3
    }
};
```

### 3. Payment Integration
Real mode integrates with complete payment flow:
- Bet deduction from real balance
- Win credit to real balance
- Jackpot contribution calculations
- Progressive jackpot win processing

## Testing Considerations

### 1. Real Money Testing
- Use test environments with test currencies
- Implement proper test data isolation
- Validate all financial calculations
- Test error scenarios thoroughly
- Test slot-specific mechanics (wilds, scatters, free spins)
- Validate RTP calculations and deductions

### 2. Integration Testing
- Test with real payment providers (in test mode)
- Validate currency exchange integration
- Test jackpot functionality
- Verify transfer operations
- Test slot game state persistence
- Validate bonus feature triggers

### 3. Compliance Testing
- Validate regulatory rule enforcement
- Test jurisdiction-specific features
- Verify audit trail completeness
- Test responsible gaming features
- Validate RTP compliance
- Test game fairness and randomness

### 4. Slot-Specific Testing
- Test reel mechanics and symbol placement
- Validate payline calculations
- Test bonus feature triggers and mechanics
- Verify free spin functionality
- Test progressive features
- Validate multi-stage game flows
