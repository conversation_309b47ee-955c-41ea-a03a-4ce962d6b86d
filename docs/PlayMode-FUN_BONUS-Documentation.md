# PlayMode.FUN_BONUS Documentation

## Overview

`PlayMode.FUN_BONUS` is a specialized play mode in the sw-management-api system that restricts gameplay to **currencies with funBonus property only**. This mode ensures no real money transactions while maintaining the same game mechanics as real money gameplay, designed specifically for FunBonus activities to help Finance calculations and increase real money traffic.

### Purpose
- Enable virtual currency gameplay with funBonus currencies only
- Support Finance calculations for FunBonus activities
- Increase real money traffic through specialized currency handling
- Maintain full gaming functionality (including jackpots) with artificial currencies
- Provide clear separation between real money and funBonus currency operations

## PlayMode Enum Structure

The PlayMode enum in `src/skywind/definitions/startGame.ts` includes:

```typescript
export enum PlayMode {
    REAL = "real",           // Real money gameplay (default)
    FUN = "fun",            // Fun/demo mode
    BNS = "bns",            // Bonus coins gameplay
    PLAY_MONEY = "play_money", // Virtual/social currency gameplay
    FUN_BONUS = "fun_bonus"  // FunBonus currency gameplay (NEW)
}
```

## Configuration

### Currency-Level Configuration

FUN_BONUS mode is controlled entirely at the currency level through the `funBonus` property:

```typescript
// Currency definition in sw-currency-exchange
interface CurrencyInfo {
    readonly name: string;
    readonly iso: string;
    readonly funBonus?: boolean; // NEW PROPERTY - enables FUN_BONUS mode
    // ... other properties
}
```

### Automatic Mode Detection

The `playModeConverter` automatically switches to FUN_BONUS mode for currencies with `funBonus: true`:

```typescript
import { playModeConverter } from "src/skywind/utils/playModeConverter";

// Automatically converts to FUN_BONUS if currency has funBonus property
const playMode = playModeConverter(PlayMode.REAL, "FUNUSD"); // Returns FUN_BONUS
```

### No Merchant Configuration Required

Unlike PLAY_MONEY mode, FUN_BONUS mode does not require merchant-level configuration:

```typescript
// validatePlaymode.ts - FUN_BONUS validation always succeeds at playmode level
export function validatePlaymode(brand: BrandEntity, playmode: PlayMode = PlayMode.REAL, merchant?: Merchant): boolean {
    if (playmode === PlayMode.PLAY_MONEY) {
        // PLAY_MONEY requires merchant configuration
        if (!(brand.isMerchant && merchant?.params?.supportPlayMoney)) {
            throw new ValidationError("Play money is not supported operation");
        }
    }
    
    // FUN_BONUS validation is handled at currency level - no merchant config needed
    return true;
}
```

## Validation

### Currency Validation

FUN_BONUS mode requires strict currency validation:

```typescript
import { validateFunBonus } from "packages/api/src/skywind/utils/validateFunBonus";

// Validates that currency has funBonus property
validateFunBonus(brand, "FUNUSD", merchant); // Throws error if currency is not funBonus enabled
```

### Error Handling

Specific error types for FUN_BONUS validation:

```typescript
// Currency not enabled for fun bonus
throw new CurrencyNotFunBonusEnabledError("USD");

// Currency not found
throw new FunBonusCurrencyNotFoundError("UNKNOWN");
```

## Game Behavior

### Jackpot Support

FUN_BONUS mode supports jackpots like REAL mode:

```typescript
// PlayMode service - jackpot support
public static supportJP(gameMode: GameMode) {
    return gameMode === "real" || gameMode === "fun_bonus" || !gameMode;
}
```

### Currency Exchange

FUN_BONUS mode uses standard currency exchange logic:

```typescript
// PlayMode service - currency exchange
if (gameData.gameTokenData.playmode === "fun_bonus") {
    // FUN_BONUS mode behaves like REAL mode for currency exchange
    return getCurrencyExchange()
        .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
}
```

### Service Factory

FUN_BONUS uses standard MerchantPlayService (no special wrapper needed):

```typescript
// defaultPlayServiceFactory.ts
switch (playmode) {
    case PlayMode.FUN_BONUS:
        // FUN_BONUS uses standard MerchantPlayService (like REAL mode)
        // No special wrapper needed - currency validation is handled elsewhere
        break;
}
```

## API Integration

### Game URL Generation

FUN_BONUS mode is supported in game URL strategies with currency validation:

```typescript
// baseGameUrlStrategy.ts
if (playMode === PlayMode.FUN_BONUS) {
    try {
        validateFunBonus(this.brand, currency);
    } catch (error) {
        return Promise.reject(error);
    }
}
```

### Token Data

FUN_BONUS mode is included in game token data:

```typescript
interface GameTokenData {
    playmode: GameMode;  // "fun_bonus" for FUN_BONUS mode
    currency: string;    // Must be a currency with funBonus: true
    // ... other fields
}
```

## Utilities

### Helper Functions

```typescript
import { 
    playModeConverter, 
    isFunBonusCurrency, 
    getFunBonusCurrencies 
} from "src/skywind/utils/playModeConverter";

// Check if currency is funBonus enabled
const isEnabled = isFunBonusCurrency("FUNUSD"); // true/false

// Get all funBonus currencies
const funBonusCurrencies = getFunBonusCurrencies(); // ["FUNUSD", "FUNEUR", ...]

// Convert playmode based on currency
const mode = playModeConverter(PlayMode.REAL, "FUNUSD"); // PlayMode.FUN_BONUS
```

## Key Differences from Other Play Modes

| Feature | REAL | PLAY_MONEY | BNS | FUN_BONUS |
|---------|------|------------|-----|-----------|
| Currency Type | Real money | Virtual/Social | Bonus coins | FunBonus currencies |
| Merchant Config | No | Required | No | No |
| Jackpot Support | Yes | No | Yes | Yes |
| Currency Exchange | Standard | Limited | Special | Standard |
| Validation Level | Basic | Merchant | Basic | Currency |

## Implementation Notes

1. **Currency-Driven**: Mode is determined by currency `funBonus` property, not merchant configuration
2. **Real-Mode Behavior**: Behaves like REAL mode for most operations (jackpots, exchange, services)
3. **Strict Validation**: Only currencies with `funBonus: true` are allowed
4. **No Breaking Changes**: Fully backward compatible with existing play modes
5. **Finance Integration**: Designed to support Finance calculations for FunBonus activities
